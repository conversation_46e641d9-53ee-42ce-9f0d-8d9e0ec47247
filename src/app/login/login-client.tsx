'use client';

import { LoginForm } from '@/components/auth/login-form';
import { useTranslation } from '@/contexts/translation-context';

export function LoginClient() {
	const { t } = useTranslation();

	return (
		<div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div className="text-center">
					<h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
						{t('auth.login.title')}
					</h1>
					<p className="text-gray-600 dark:text-gray-400">{t('auth.login.welcome')}</p>
				</div>
				<LoginForm />
			</div>
		</div>
	);
}
