'use client';

import { ReactNode } from 'react';
import { CollectionsProvider } from '@/contexts/collections-context';
import { CollectionWithDetail } from '@/models';

interface CollectionsLayoutClientProps {
	children: ReactNode;
	initialCollections: CollectionWithDetail[];
}

export default function CollectionsLayoutClient({
	children,
	initialCollections,
}: CollectionsLayoutClientProps) {
	return (
		<CollectionsProvider initialCollections={initialCollections}>
			{children}
		</CollectionsProvider>
	);
}
